import React, { useState, useEffect, useRef } from 'react';
import { Input, Select, Card, Button, Spin, Empty } from 'antd';
// import SortArrowIconUrl from '@src/assets/icons/sort-arrow.svg'; // Assuming you have a similar icon
// import SearchIconUrl from '@src/assets/icons/search.svg'; // Assuming you have a similar icon
import DefaultCourseImageUrl from '@src/asset/image/project-default.svg'; // Placeholder, replace with actual default course image
import { useNavigate, useLocation } from 'react-router-dom';
import { LINK } from '@src/constants/link'; // Sử dụng đường dẫn LINK
import { useTranslation } from 'react-i18next';
import { getAllCourses } from '@src/app/services/RolePlay'; // Import service từ RolePlay
import './CoursesListScreen.scss';
import Loading from '@src/app/component/Loading';
import { API } from '@src/constants/api';
import CheckGreenBoder from '@src/app/component/SvgIcons/Check/CheckGreenBorder';
import Icon_LIST_COURSES from '@src/asset/icon/list-courese-icon.svg';
import Icon_Lock from '@src/asset/icon/course/lock-icon.svg';
import Polygon from '@src/asset/icon/course/Polygon.svg';
import useDebounce from '@src/app/hooks/useDebounce';

const { Option } = Select;
const PAGE_SIZE = 100; // Adjust as needed
const START_PAGE = 1;

export const CoursesListScreen = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const searchTimeout = useRef(null);

  const [courses, setCourses] = useState([]);
  const [allCourses, setAllCourses] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300); // ✅ debounce search

  const [selectedType, setSelectedType] = useState(null);
  const [sortOrderByTime, setSortOrderByTime] = useState('asc');
  const [sortOrderByName, setSortOrderByName] = useState('asc');
  const [sortField, setSortField] = useState('createdAt');

  const [hoveredCourseId, setHoveredCourseId] = useState(null);

  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(START_PAGE);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const buildQuery = () => {
    const query = {};
    if (debouncedSearchQuery) query.name = debouncedSearchQuery;
    if (selectedType) query.simulationType = selectedType;

    let sortOrder = 'asc';
    if (sortField === 'createdAt') {
      sortOrder = sortOrderByTime;
    } else if (sortField === 'name') {
      sortOrder = sortOrderByName;
    }

    if (sortField && sortOrder) {
      query.sort = sortOrder === 'asc' ? sortField : `-${sortField}`;
    }

    return query;
  };

  const buildPaging = () => ({
    page,
    limit: PAGE_SIZE,
  });

  function splitByNewLines(text) {
    return text
      .split(/\n+/)
      .map(str => str.trim())
      .filter(str => str.length > 0);
  }

  const updateUrlParams = () => {
    const params = new URLSearchParams();
    if (debouncedSearchQuery) params.set('search', debouncedSearchQuery);
    if (selectedType) params.set('type', selectedType);
    if (sortField) {
      params.set('sortField', sortField);
      const sortOrder = sortField === 'createdAt' ? sortOrderByTime : sortOrderByName;
      params.set('sortOrder', sortOrder);
    }
    if (page !== START_PAGE) params.set('page', page.toString());
    const newUrl = `${location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const query = buildQuery();
      const paging = buildPaging();
      const response = await getAllCourses({ ...query, status: 'published' }, paging);

      if (response && response.rows) {
        const formatted = response.rows.map(course => ({
          id: course._id,
          title: course.name,
          description: splitByNewLines(course.description),
          time: `${course.totalEstimatedCallTimeInMinutes || 0} phút`,
          progress: `${course.userCompletionPercentage || 0}% hoàn thành`,
          image: course?.thumbnailId
            ? API.STREAM_ID.format(course.thumbnailId)
            : DefaultCourseImageUrl,
          type: course.simulationType,
        }));

        if (page === START_PAGE) {
          setCourses(formatted);
          setAllCourses(formatted);
        } else {
          setCourses(prev => [...prev, ...formatted]);
          setAllCourses(prev => [...prev, ...formatted]);
        }

        setTotalCount(response.count || 0);
        setHasMore(page * PAGE_SIZE < response.total);
      } else {
        setCourses([]);
        setAllCourses([]);
        setTotalCount(0);
        setHasMore(false);
      }
    } catch (err) {
      console.error('Fetch courses error:', err);
      setCourses([]);
      setAllCourses([]);
      setTotalCount(0);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    updateUrlParams();
    fetchCourses();
  }, [page, debouncedSearchQuery, selectedType, sortField, sortOrderByName, sortOrderByTime]);

  const handleSearch = value => {
    setSearchQuery(value);
    setPage(START_PAGE);
  };

  const handleTypeChange = value => {
    setSelectedType(value);
    setPage(START_PAGE);
  };

  const toggleSortByTime = () => {
    const newOrder = sortOrderByTime === 'asc' ? 'desc' : 'asc';
    setSortOrderByTime(newOrder);
    setSortField('createdAt');
  };

  const toggleSortByName = () => {
    const newOrder = sortOrderByName === 'asc' ? 'desc' : 'asc';
    setSortOrderByName(newOrder);
    setSortField('name');
  };

  const handleCourseClick = id => {
    navigate(LINK.ROLE_PLAY_SESSION.format(id));
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  // Icons - replace with actual imports if you have them
  const SearchIconUrl =
    "data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20clip-path%3D%22url(%23clip0_47_257)%22%3E%3Cpath%20d%3D%22M12.3333%2012.3333L14.6667%2014.6667M14%207.66666C14%204.16886%2011.1645%201.33333%207.66668%201.33333C4.16887%201.33333%201.33334%204.16886%201.33334%207.66666C1.33334%2011.1645%204.16887%2014%207.66668%2014C11.1645%2014%2014%2011.1645%2014%207.66666Z%22%20stroke%3D%22%230C4DA2%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22/%3E%3C/g%3E%3Cdefs%3E%3CclipPath%20id%3D%22clip0_47_257%22%3E%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22white%22/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E"
  const SortArrowIconUrl =
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z'/%3E%3C/svg%3E";

  return (
    <div className="courses-list-screen">
      <div className="courses-list-screen__container">
        <div className="courses-list-screen__header">
          <div className='flex gap-2 mb-4 items-center'>
            <img src={Icon_LIST_COURSES} alt='icon' />
            <h2 className='courses-list-screen__title'>Danh sách khóa học kỹ năng</h2>
          </div>
          <div className='flex justify-between items-center'>
            <div className="courses-list-screen__search-container">
              <Input
                className="courses-list-screen__search-input"
                placeholder={t('SEARCH_COURSE_PLACEHOLDER_STB', 'Tìm kiếm khóa học...')} // Example translation key
                suffix={<img src={SearchIconUrl} alt="Search" className="courses-list-screen__search-icon" />}
                onChange={e => handleSearch(e.target.value)}
                value={searchQuery}
              />
              <Select
                className="courses-list-screen__type-select"
                placeholder={t('COURSE_TYPE_PLACEHOLDER_STB', 'Loại khóa học')} // Example translation key
                onChange={handleTypeChange}
                allowClear
                value={selectedType}
              >
                <Option value="Sale">{t('COURSE_TYPE_SALE', 'Sale')}</Option>
                <Option value="Service">{t('COURSE_TYPE_SERVICE', 'Service')}</Option>
                <Option value="HR">{t('COURSE_TYPE_HR', 'HR')}</Option>
                <Option value="Education">{t('COURSE_TYPE_EDUCATION', 'Education')}</Option>
              </Select>
            </div>
            <div className='flex gap-2'>
              <Button className="courses-list-screen__sort-button" onClick={toggleSortByTime}>
                {sortOrderByTime === 'asc' ? 'Mới nhất đến cũ nhất' : 'Cũ nhất đến mới nhất'}
                <img
                  src={SortArrowIconUrl}
                  alt="Sort direction"
                  className={`courses-list-screen__sort-icon ${sortOrderByTime === 'desc' ? 'courses-list-screen__sort-icon--desc' : ''
                    }`}
                />
              </Button>
              <Button className="courses-list-screen__sort-button" onClick={toggleSortByName}>
                {sortOrderByName === 'asc' ? 'A-Z' : 'Z-A'}
                <img
                  src={SortArrowIconUrl}
                  alt="Sort direction"
                  className={`courses-list-screen__sort-icon ${sortOrderByName === 'desc' ? 'courses-list-screen__sort-icon--desc' : ''
                    }`}
                />
              </Button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="courses-list-screen__loading">
            <Loading active transparent />
          </div>
        ) : !loading && courses?.length === 0 ? (
          <Empty
            description={t('NO_COURSES_FOUND', 'No courses found.')} // Example translation key
            className="courses-list-screen__empty"
          />
        ) : (
          <>
            <div className="courses-list-screen__courses-grid">
              {courses?.map(course => (
                <Card
                  key={course.id}
                  className="courses-list-screen__course-card"
                  hoverable
                  onClick={() => handleCourseClick(course.id)}
                  onMouseEnter={() => {
                    if (course.lock) setHoveredCourseId(course.id);
                  }}
                  onMouseLeave={() => setHoveredCourseId(null)}
                  cover={
                    <img
                      className="course-image"
                      alt={course.title}
                      src={course.image}
                      onError={e => {
                        e.target.onerror = null; // prevents looping
                        e.target.src = DefaultCourseImageUrl;
                      }}
                    />
                  }
                >
                  <div className="course-card__content-wrapper">
                    <div className='time-progress-wrapper'>
                      <p className="course-time">{course.time}</p>
                      <p className="course-progress">{course.progress}</p>
                    </div>
                    <div className="course-title">{course.lock && <img
                      src={Icon_Lock} alt='' className='mr-2 -mb-1.5 relative'
                    />}
                      {course.title}
                    </div>
                    {hoveredCourseId === course.id && (
                      <div className='absolute bottom-[38%] left-0 bg-transparent w-[95%] '>
                        <div className="px-4 py-2 rounded-[16px] z-50 bg-[#FFF4E2] text-black text-[14px] leading-[24px] -mb-2 shadow-lg ">
                          Bạn cần hoàn thành các khóa học trước khi thực hành với AI Coach
                        </div>
                        <img src={Polygon} alt='' className='ml-[6.5%] ' />
                      </div>
                    )}
                    {course.description?.map((line, index) => {
                      return (
                        <div className='flex gap-1' key={index}>
                          <CheckGreenBoder width="17" height="16" />
                          <p className="course-description">{line}</p>
                        </div>
                      )
                    })}
                  </div>
                </Card>
              ))}
            </div>

            {hasMore && (
              <div className="courses-list-screen__load-more">
                <Button
                  onClick={handleLoadMore}
                  loading={loading && courses?.length > 0} // Show loading on button only if loading more
                  className="courses-list-screen__load-more-button"
                >
                  {t('LOAD_MORE_STB', 'Xem thêm')}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
