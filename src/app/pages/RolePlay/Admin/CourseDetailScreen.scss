.course-detail-container {
  .course-detail-header-card {
    margin-bottom: 24px;

    .course-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .course-detail-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .course-detail-description {
        margin: 4px 0 0;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  .course-detail-form-card {
    .ant-card-head-title {
      font-weight: 600;
    }

    .ant-upload-picture-card-wrapper {
      // display: inline-block;
    }

    .ant-upload.ant-upload-select-picture-card {
      width: 104px;
      height: 104px;
      margin-right: 8px;
      margin-bottom: 8px;
      background-color: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 2px;
      cursor: pointer;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }

      .ant-upload-disabled {
        cursor: not-allowed;
      }
    }

    .ant-upload-list-picture-card .ant-upload-list-item {
      width: 104px;
      height: 104px;
      padding: 0;
    }

    .ant-upload-list-item-info {
      > span {
        display: block;
        width: 100%;
        height: 100%;
      }

      img {
        object-fit: cover;
      }
    }

  }

  .btn-cancel {
    // Custom styles if needed
  }

  .btn-save {
    min-width: 100px;
  }
}

// Professional card layout for reference materials
.reference-materials-section {
  .reference-empty-state {
    text-align: center;
    padding: 60px 20px;

    .ant-empty {
      margin: 0;

      .ant-empty-description {
        color: #8c8c8c;
        font-size: 14px;
        margin-bottom: 16px;
      }
    }
  }

  // Controls header
  .reference-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    .reference-info {
      .reference-count {
        color: #595959;
        font-size: 14px;
      }
    }

    .reference-view-controls {
      .ant-btn-group {
        .ant-btn {
          // Minimal fix - chỉ đảm bảo visibility
          &:not(.ant-btn-primary) {
            border: 1px solid #d9d9d9;
            background-color: #ffffff;
            color: rgba(0, 0, 0, 0.65);
          }

          &:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }

          &:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }

          &:not(:first-child) {
            margin-left: -1px;
          }
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
  }


}

  // Horizontal scroll container for cards
  .reference-cards-scroll-container {
    position: relative;
    margin-top: 8px;

    .reference-cards-scroll {
      display: flex;
      gap: 16px;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 8px 0 16px 0;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;

      // Custom scrollbar styling
      &::-webkit-scrollbar {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: #a8a8a8;
        }
      }

      // Firefox scrollbar
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;

      @media (max-width: 768px) {
        gap: 12px;
        padding: 8px 0 12px 0;

        &::-webkit-scrollbar {
          height: 4px;
        }
      }
    }
  }

  .reference-card {
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
      transform: translateY(-2px);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }

    .ant-card-body {
      padding: 20px;
    }

    &.compact .ant-card-body {
      padding: 16px;
    }

    &.horizontal {
      min-width: 280px;
      max-width: 320px;
      flex-shrink: 0;

      .ant-card-body {
        padding: 16px;
      }

      @media (max-width: 768px) {
        min-width: 250px;
        max-width: 280px;

        .ant-card-body {
          padding: 14px;
        }
      }

      @media (max-width: 480px) {
        min-width: 220px;
        max-width: 250px;

        .ant-card-body {
          padding: 12px;
        }
      }
    }

    .reference-card-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;
    }

    &.compact .reference-card-content {
      gap: 12px;
    }

    &.horizontal .reference-card-content {
      gap: 12px;
    }

    .reference-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .reference-icon-wrapper {
        flex-shrink: 0;

        .reference-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #1890ff;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);

          &:hover {
            background: linear-gradient(135deg, #bae7ff 0%, #91d5ff 100%);
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .reference-title {
        flex: 1;
        min-width: 0;

        .reference-link {
          color: #262626;
          text-decoration: none;
          font-weight: 600;
          font-size: 15px;
          line-height: 1.4;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: all 0.2s ease;

          &:hover {
            color: #1890ff;
            transform: translateX(2px);
          }
        }
      }
    }

    .reference-meta {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .reference-tags {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .reference-type-tag,
        .reference-status-tag {
          border-radius: 6px;
          font-weight: 500;
          font-size: 12px;
          padding: 4px 8px;
          border: none;
          display: flex;
          align-items: center;
          gap: 4px;

          &.ant-tag-blue {
            background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
            color: #1890ff;
            border: 1px solid #91d5ff;
          }

          &.ant-tag-green {
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }

          &.ant-tag-success {
            background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }

          &.ant-tag-warning {
            background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
            color: #fa8c16;
            border: 1px solid #ffb347;
          }
        }
      }

      .reference-date {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #8c8c8c;
        font-size: 12px;
        font-weight: 400;

        .anticon {
          font-size: 12px;
        }
      }
    }

    .reference-actions {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;
      margin-top: auto;

      .reference-action-btn {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &:hover {
          transform: scale(1.1);
          border-color: currentColor;
        }

        .anticon {
          font-size: 14px;
        }

        &.view-btn {
          color: #1890ff;

          &:hover {
            background-color: #e6f7ff;
            color: #096dd9;
          }
        }

        &.download-btn {
          color: #52c41a;

          &:hover {
            background-color: #f6ffed;
            color: #389e0d;
          }
        }

        &.edit-btn {
          color: #fa8c16;

          &:hover {
            background-color: #fff7e6;
            color: #d46b08;
          }
        }

        &.delete-btn {
          color: #ff4d4f;

          &:hover {
            background-color: #fff2f0;
            color: #cf1322;
          }
        }
      }
    }

    // Mobile responsive
    @media (max-width: 480px) {
      .ant-card-body {
        padding: 16px;
      }

      .reference-card-content {
        gap: 12px;
      }

      .reference-header {
        .reference-icon-wrapper .reference-icon {
          width: 36px;
          height: 36px;
          font-size: 16px;
        }

        .reference-title .reference-link {
          font-size: 14px;
        }
      }

      .reference-actions {
        .reference-action-btn {
          width: 28px;
          height: 28px;

          .anticon {
            font-size: 12px;
          }
        }
      }
    }
  }

  // List View Styling
  .reference-list-view {
    .reference-list-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      margin-bottom: 8px;
      background-color: #ffffff;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1890ff;
        background-color: #f8f9fa;
        transform: translateX(2px);
      }

      .reference-list-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #1890ff;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .reference-list-content {
        flex: 1;
        min-width: 0;

        .reference-list-title {
          margin-bottom: 4px;

          .reference-list-link {
            color: #262626;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &:hover {
              color: #1890ff;
            }
          }
        }

        .reference-list-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-wrap: wrap;

          .reference-list-date {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }

      .reference-list-actions {
        display: flex;
        gap: 4px;
        flex-shrink: 0;

        .ant-btn {
          width: 28px;
          height: 28px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            transform: scale(1.1);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .reference-list-icon {
          margin-right: 0;
          margin-bottom: 4px;
        }

        .reference-list-actions {
          align-self: flex-end;
        }
      }
    }
  }

// Clean empty state styling
.ant-empty {
  padding: 40px 20px;

  .ant-empty-description {
    color: #8c8c8c;
    font-size: 14px;
  }
}

// Public checkbox styling
.reference-public-checkbox {
  .ant-checkbox-wrapper {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}

// Card header improvements
.ant-card-head {
  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .ant-card-extra {
    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
