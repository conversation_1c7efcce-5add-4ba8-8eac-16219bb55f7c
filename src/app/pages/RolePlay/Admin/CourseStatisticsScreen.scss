.course-statistics-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-header-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .statistics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;

        .header-info {
          .statistics-title {
            margin: 0 0 4px 0;
            font-weight: 600;
            font-size: 24px;
          }

          .course-name {
            color: #666;
            font-size: 14px;
            margin: 0;
          }
        }
      }

      .header-right {
        .back-button {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 120px;
          height: 40px;
          border-radius: 8px;
          font-weight: 500;
          border: 1px solid #d9d9d9 !important;
          background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            border-color: #40a9ff !important;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
          }

          &:focus {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
          }

          &:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }

  .statistics-filter-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .filter-form-item {
        margin-bottom: 0;
      }

      .filter-info {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 8px;

        .filter-label {
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
        }
      }

      .filter-buttons-col {
        .filter-buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;

          .ant-btn {
            min-width: 120px;
            height: 42px;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border: 1px solid #d9d9d9 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
              border-color: #40a9ff !important;

              &::before {
                opacity: 1;
              }
            }

            &:focus {
              border-color: #1890ff !important;
              box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
            }

            &:active {
              transform: translateY(-1px);
              transition: all 0.1s ease;
            }

            // Specific styling for different button types
            &.ant-btn-default {
              background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
              color: #595959;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              &:hover {
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
                color: #262626;
              }
            }

            &.ant-btn-primary {
              background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
              border-color: #1890ff !important;
              color: #fff;
              box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

              &:hover {
                background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
                border-color: #096dd9 !important;
                box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
              }
            }

            .anticon {
              font-size: 16px;
            }
          }
        }
      }

      // Custom DatePicker styling
      .ant-picker {
        height: 40px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-picker-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-picker-input > input {
          font-size: 14px;
        }
      }
    }
  }

  .statistics-overview-card,
  .completion-rate-card,
  .top-students-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head-title {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .completion-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .completion-details {
    padding: 20px;

    .detail-item {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .rank-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .student-name {
    display: flex;
    align-items: center;
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;

    .statistics-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-left {
        .header-info {
          text-align: center;

          .statistics-title {
            font-size: 20px;
          }
        }
      }

      .header-right {
        .back-button {
          width: 100%;
          justify-content: center;
          min-width: auto;
        }
      }
    }

    .statistics-filter-card {
      .form-filter {
        .filter-buttons-col {
          margin-top: 16px;

          .filter-buttons {
            justify-content: stretch;

            .ant-btn {
              flex: 1;
            }
          }
        }

        .filter-info {
          justify-content: center;
          margin-top: 8px;
        }
      }
    }

    .completion-details {
      padding: 16px;
    }
  }

  @media (max-width: 576px) {
    .statistics-filter-card {
      .form-filter {
        .filter-buttons {
          flex-direction: column;
          gap: 8px;
        }
      }
    }
  }

  // Table customization
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // Statistics cards
  .ant-statistic {
    .ant-statistic-title {
      font-weight: 500;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-weight: 600;
    }
  }

  // Progress circle customization
  .ant-progress-circle {
    .ant-progress-text {
      font-weight: 600;
      font-size: 24px;
    }
  }

  // Tag customization
  .ant-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
  }
}
