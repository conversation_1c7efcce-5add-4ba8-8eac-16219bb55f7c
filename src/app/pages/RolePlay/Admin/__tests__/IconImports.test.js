import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';
import configureStore from 'redux-mock-store';

// Import all admin components to test icon imports
import CoursesManagementScreen from '../CoursesManagementScreen';
import AIPersonaManagementScreen from '../AIPersonaManagementScreen';
import RolePlayInstructionManagementScreen from '../RolePlayInstructionManagementScreen';
import CourseStatisticsScreen from '../CourseStatisticsScreen';
import StatisticsOverviewScreen from '../StatisticsOverviewScreen';
import i18n from '@translations/i18n';

const mockStore = configureStore([]);

const createMockStore = () => {
  return mockStore({
    auth: {
      user: {
        id: 'test-user-id',
        name: 'Test User',
        isSystemAdmin: true,
        role: 'admin'
      }
    }
  });
};

const renderWithProviders = (component) => {
  return render(
    <Provider store={createMockStore()}>
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          {component}
        </I18nextProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ courseId: 'test-course-id' }),
}));

describe('Icon Imports Test', () => {
  test('CoursesManagementScreen renders without icon import errors', () => {
    expect(() => {
      renderWithProviders(<CoursesManagementScreen />);
    }).not.toThrow();
  });

  test('AIPersonaManagementScreen renders without icon import errors', () => {
    expect(() => {
      renderWithProviders(<AIPersonaManagementScreen />);
    }).not.toThrow();
  });

  test('RolePlayInstructionManagementScreen renders without icon import errors', () => {
    expect(() => {
      renderWithProviders(<RolePlayInstructionManagementScreen />);
    }).not.toThrow();
  });

  test('CourseStatisticsScreen renders without icon import errors', () => {
    expect(() => {
      renderWithProviders(<CourseStatisticsScreen />);
    }).not.toThrow();
  });

  test('StatisticsOverviewScreen renders without icon import errors', () => {
    expect(() => {
      renderWithProviders(<StatisticsOverviewScreen />);
    }).not.toThrow();
  });

  test('all components have required icons available', () => {
    // Test that CalendarOutlined and SearchOutlined are available
    const { CalendarOutlined, SearchOutlined } = require('@ant-design/icons');
    
    expect(CalendarOutlined).toBeDefined();
    expect(SearchOutlined).toBeDefined();
    
    // Test that icons can be rendered
    expect(() => {
      render(<CalendarOutlined />);
      render(<SearchOutlined />);
    }).not.toThrow();
  });
});
