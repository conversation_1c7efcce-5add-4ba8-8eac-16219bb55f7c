import React, {useEffect, useState, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {Card, Col, Form, Input, InputNumber, Row, Select, Upload, Button, Space, Typography, message, Tooltip, Empty, Tag, Pagination, Collapse} from 'antd';
import {useNavigate, useParams} from 'react-router-dom';
import {
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  FileTextOutlined,
  EditOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  DownloadOutlined,
  LinkOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileOutlined,
  CalendarOutlined,
  CloudDownloadOutlined,
  InboxOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';

import {LINK} from '@link';
import {BUTTON} from '@constant';

import AntButton from '@component/AntButton';
import Loading from '@component/Loading';
import {AntForm} from '@component/AntForm';
import {toast} from '@component/ToastProvider';
import UploadImagePreview from '@component/UploadImagePreview'; // <PERSON><PERSON><PERSON> sử có component này hoặc sẽ tạo

import {getCourseDetails, createCourse, updateCourse} from '@services/RolePlay/CourseService';
import {uploadFile, deleteFile} from '@services/File';

import ReferenceContentModal from './components/ReferenceContentModal';
import EditReferenceModal from './components/EditReferenceModal';
import GoogleSheetsImportModal from './components/GoogleSheetsImportModal';

import AIScenarioTabs from './components/AIScenarioTabs';
import UploadReference from './UploadReference';
import {API} from '@api';

import './CourseDetailScreen.scss';

const {TextArea} = Input;
const {Option} = Select;

const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

// Helper function để lấy icon phù hợp cho file type
const getFileIcon = (fileName, type) => {
  if (type === 'url') {
    return <LinkOutlined />;
  }

  if (!fileName) return <FileOutlined />;

  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return <FilePdfOutlined />;
    case 'doc':
    case 'docx':
      return <FileWordOutlined />;
    case 'xls':
    case 'xlsx':
      return <FileExcelOutlined />;
    case 'ppt':
    case 'pptx':
      return <FilePptOutlined />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
      return <FileImageOutlined />;
    default:
      return <FileOutlined />;
  }
};

// Helper function để format ngày tháng
const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Helper function để format file size
const formatFileSize = (bytes) => {
  if (!bytes) return '';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const CourseDetailScreen = () => {
  const {id} = useParams();
  const navigate = useNavigate();
  const {t} = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // State cho AI Scenarios
  const [scenarios, setScenarios] = useState([]);
  const [selectedSimulationType, setSelectedSimulationType] = useState('');
  const [taskDetailsList, setTaskDetailsList] = useState([]);

  // Reference to AIScenarioTabs component
  const scenarioTabsRef = useRef();

  // Thay thế state cũ bằng state mới để quản lý references
  const [references, setReferences] = useState([]);

  // State cho modal upload reference
  const [isShowUploadReference, setShowUploadReference] = useState(false);

  // Loại bỏ state cho modal create AI Persona vì đã chuyển sang AI Scenarios

  // State cho modal hiển thị nội dung reference
  const [isShowReferenceContent, setShowReferenceContent] = useState(false);
  const [selectedReference, setSelectedReference] = useState(null);

  // State cho modal edit reference
  const [isShowEditReference, setShowEditReference] = useState(false);
  const [editingReference, setEditingReference] = useState(null);

  // State cho modal Google Sheets import
  const [isShowGoogleSheetsImport, setShowGoogleSheetsImport] = useState(false);

  // State cho view mode của references
  const [referenceViewMode, setReferenceViewMode] = useState('card'); // 'card' hoặc 'list'

  // Thumbnail related state
  const [thumbnailId, setThumbnailId] = useState(null);
  const [initialThumbnailIdOnLoad, setInitialThumbnailIdOnLoad] = useState(null);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(false);

  useEffect(() => {
    if (id) {
      setIsEditMode(true);
      fetchCourseDetails(id);
    } else {
      setIsEditMode(false);
      form.resetFields();
      setThumbnailId(null);
      setInitialThumbnailIdOnLoad(null);
      setTaskDetailsList([]);
      form.setFieldsValue({thumbnailId: null});
    }
  }, [id, form]);

  useEffect(() => {
    form.setFieldsValue({thumbnailId: thumbnailId});
  }, [thumbnailId, form]);

  // Lắng nghe thay đổi của simulationType để lọc roleplayInstructions
  useEffect(() => {
    const simulationType = form.getFieldValue('simulationType');
    setSelectedSimulationType(simulationType);
  }, [form]);

  // Loại bỏ logic theo dõi unsaved tasks vì cảnh báo đã được hiển thị trong AIScenarioTabs

  const fetchCourseDetails = async courseId => {
    setIsLoading(true);
    try {
      // Chỉ populate references vì aiPersonaId, taskIds, roleplayInstructionId đã chuyển sang AI Scenarios
      const populateOpts = ['references'];
      const apiResponse = await getCourseDetails(courseId, populateOpts);
      if (apiResponse) {
        form.setFieldsValue({
          ...apiResponse,
          // Loại bỏ các field đã chuyển sang AI Scenarios
        });

        if (apiResponse.simulationType) {
          setSelectedSimulationType(apiResponse.simulationType);
        }

        if (apiResponse.thumbnailId) {
          setThumbnailId(apiResponse.thumbnailId); // Giả sử thumbnailId là một string ID
          setInitialThumbnailIdOnLoad(apiResponse.thumbnailId);
          form.setFieldsValue({thumbnailId: apiResponse.thumbnailId});
        } else {
          setThumbnailId(null);
          setInitialThumbnailIdOnLoad(null);
          form.setFieldsValue({thumbnailId: null});
        }

        // Cập nhật state references từ API response
        if (apiResponse.references && apiResponse.references.length > 0) {
          setReferences(apiResponse.references);
        } else {
          setReferences([]);
        }

        // Loại bỏ xử lý taskIds vì đã chuyển sang AI Scenarios
      } else {
        toast.error(t('ERROR_LOADING_COURSE_DATA'));
        navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
      }
    } catch (error) {
      console.error('Error fetching course details:', error);
      toast.error(t('ERROR_LOADING_COURSE_DATA'));
      navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUploadThumbnail = async file => {
    setIsLoadingThumbnail(true);
    try {
      const uploadResponse = await uploadFile(file, {folder: 'course_thumbnails'});
      if (uploadResponse && uploadResponse._id) {
        setThumbnailId(uploadResponse._id);
        toast.success(t('UPLOAD_THUMBNAIL_SUCCESS'));
      } else {
        toast.error(t('UPLOAD_THUMBNAIL_ERROR'));
      }
    } catch (error) {
      console.error('Thumbnail upload error:', error);
      toast.error(t('UPLOAD_THUMBNAIL_ERROR'));
    } finally {
      setIsLoadingThumbnail(false);
    }
  };

  const handleClearThumbnail = () => {
    setThumbnailId(null);
  };

  // Xử lý khi thêm references từ modal
  const handleReferenceAdded = addedReferences => {
    // Cập nhật state references với references mới được thêm từ modal
    setReferences(prevReferences => {
      // Lọc ra các references đã tồn tại để tránh trùng lặp
      const existingIds = prevReferences.map(ref => ref._id);
      const newReferences = addedReferences.filter(ref => !existingIds.includes(ref._id));
      return [...prevReferences, ...newReferences];
    });
  };

  // Loại bỏ handleAiPersonaChange vì đã chuyển sang AI Scenarios

  const handleSimulationTypeChange = value => {
    setSelectedSimulationType(value);
  };

  // Callback khi scenarios thay đổi
  const handleScenariosChange = (updatedScenarios) => {
    setScenarios(updatedScenarios);
  };

  // Task management functions
  const handleAddTask = (newTask) => {
    setTaskDetailsList(prevTasks => [...prevTasks, newTask]);
  };

  const handleTaskUpdate = (updatedTaskRow) => {
    setTaskDetailsList(prevTasks =>
      prevTasks.map(task => {
        if (updatedTaskRow.original_temp_id && task._id === updatedTaskRow.original_temp_id) {
          const {original_temp_id, ...taskFromServer} = updatedTaskRow;
          return taskFromServer;
        } else if (task._id === updatedTaskRow._id) {
          return {...task, ...updatedTaskRow};
        }
        return task;
      }),
    );
  };

  const handleTaskDelete = (taskIdToDelete) => {
    setTaskDetailsList(prevTasks => prevTasks.filter(task => task._id !== taskIdToDelete));
  };

  const handleFormSubmit = async values => {
    setIsLoading(true);

    try {
      // Lưu tất cả scenarios trước khi lưu course
      if (scenarioTabsRef.current) {
        console.log('Saving scenarios before course...');
        const scenariosSaved = await scenarioTabsRef.current.saveAllScenarios();
        if (!scenariosSaved) {
          console.log('Failed to save scenarios');
          setIsLoading(false);
          return;
        }
        console.log('Scenarios saved successfully');
      }

      let oldThumbnailIdToDelete = null;
      if (initialThumbnailIdOnLoad && initialThumbnailIdOnLoad !== values.thumbnailId) {
        oldThumbnailIdToDelete = initialThumbnailIdOnLoad;
      }

      // Lấy danh sách ID của references
      const referenceIds = references.map(ref => ref._id);

      const payloadToSend = {
        ...values,
        thumbnailId: values.thumbnailId, // Đã được sync từ state
        references: referenceIds, // Sử dụng references mới thay vì referenceUrls và referenceFiles
        // Không cần gửi taskIds vì đã chuyển sang AI Scenarios
      };

      let apiResponse;
      if (isEditMode) {
        apiResponse = await updateCourse({...payloadToSend, _id: id});
      } else {
        apiResponse = await createCourse(payloadToSend);
      }

      if (apiResponse) {
        if (oldThumbnailIdToDelete) {
          try {
            await deleteFile(oldThumbnailIdToDelete);
          } catch (deleteError) {
            console.error('Failed to delete old thumbnail:', oldThumbnailIdToDelete, deleteError);
          }
        }

        if (isEditMode) {
          toast.success(t('UPDATE_COURSE_SUCCESS'));
          fetchCourseDetails(id); // Tải lại để cập nhật UI
        } else {
          toast.success(t('CREATE_COURSE_SUCCESS'));
          if (apiResponse._id) {
            navigate(LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(apiResponse._id));
          }
        }
      } else {
        if (isEditMode) {
          toast.error(t('UPDATE_COURSE_ERROR'));
        } else {
          toast.error(t('CREATE_COURSE_ERROR'));
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
  };

  // Xử lý xóa reference
  const handleRemoveReference = referenceId => {
    setReferences(prevReferences => prevReferences.filter(ref => ref._id !== referenceId));
  };

  // Xử lý edit reference
  const handleEditReference = reference => {
    setEditingReference(reference);
    setShowEditReference(true);
  };

  // Xử lý cập nhật reference sau khi edit
  const handleReferenceUpdated = updatedReference => {
    setReferences(prevReferences =>
      prevReferences.map(ref =>
        ref._id === updatedReference._id ? updatedReference : ref,
      ),
    );
    setShowEditReference(false);
    setEditingReference(null);
  };

  // Xử lý khi chọn dữ liệu từ Google Sheets
  const handleGoogleSheetsDataSelected = (selectedData) => {
    // Điền dữ liệu vào form
    const formData = {
      name: selectedData.name || '',
      description: selectedData.description || '',
      estimatedCallTimeInMinutes: selectedData.estimatedCallTimeInMinutes || 0,
      simulationType: selectedData.simulationType || '',
      status: selectedData.status || 'draft',
    };

    // Cập nhật form với dữ liệu từ Google Sheets
    form.setFieldsValue(formData);

    // Cập nhật simulation type để lọc roleplay instructions
    if (selectedData.simulationType) {
      setSelectedSimulationType(selectedData.simulationType);
    }

    // Chỉ hiển thị một thông báo thành công duy nhất
    toast.success(t('DATA_IMPORTED_SUCCESS', 'Đã nhập dữ liệu thành công!'));
  };


  const saveButtonDisabled = isLoading || isLoadingThumbnail;

  return (
    <Loading active={isLoading} transparent>
      <div className="course-detail-container">
        <Card className="course-detail-header-card">
          <div className="course-detail-header">
            <div>
              <h1 className="course-detail-title">{isEditMode ? t('EDIT_COURSE') : t('CREATE_COURSE')}</h1>
              <p className="course-detail-description">
                {isEditMode ? t('EDIT_COURSE_DESCRIPTION') : t('CREATE_COURSE_DESCRIPTION')}
              </p>
            </div>
          </div>
        </Card>

        <AntForm
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          className="course-detail-form-card"
          initialValues={{status: 'draft'}}
        >
          <Card
            title={t('COURSE_INFORMATION')}
            extra={<AntButton
              type={BUTTON.DEEP_NAVY}
              size="small"
              icon={<PlusOutlined />}
              onClick={() => setShowGoogleSheetsImport(true)}
            >
              {t('IMPORT_FROM_LMS', 'Lấy dữ liệu LMS')}
            </AntButton>}>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label={t('COURSE_NAME', 'Course Name')}
                  rules={[
                    {required: true, message: t('PLEASE_INPUT_COURSE_NAME', 'Please input the course name!')},
                    {max: 255, message: t('COURSE_NAME_TOO_LONG', 'Course name cannot exceed 255 characters')},
                  ]}
                >
                  <Input placeholder={t('ENTER_COURSE_NAME', 'Enter course name')} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="estimatedCallTimeInMinutes"
                  label={t('ESTIMATED_CALL_TIME', 'Estimated Call Time (minutes)')}
                  rules={[
                    {type: 'number', min: 0, message: t('TIME_MUST_BE_POSITIVE', 'Time must be a positive number')},
                  ]}
                >
                  <InputNumber
                    style={{width: '100%'}}
                    placeholder={t('ENTER_ESTIMATED_TIME', 'Enter estimated time in minutes')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="status"
                  label={t('COURSE_STATUS', 'Course Status')}
                  rules={[{required: true, message: t('PLEASE_SELECT_COURSE_STATUS', 'Please select course status!')}]}
                >
                  <Select placeholder={t('SELECT_COURSE_STATUS', 'Select course status')} allowClear>
                    <Option value="draft">{t('DRAFT', 'Draft')}</Option>
                    <Option value="published">{t('PUBLISHED', 'Published')}</Option>
                    <Option value="archived">{t('ARCHIVED', 'Archived')}</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label={t('COURSE_THUMBNAIL')}>
              <UploadImagePreview
                imageId={thumbnailId} // Sử dụng thumbnailId đã được fetch hoặc upload
                onDrop={handleUploadThumbnail}
                onClear={handleClearThumbnail}
                loading={isLoadingThumbnail}
                // Cần truyền URL của ảnh nếu thumbnailId chỉ là ID và không phải object File đầy đủ có URL
                // imageUrl={thumbnailId && typeof thumbnailId === 'object' && thumbnailId.url ? thumbnailId.url : (typeof thumbnailId === 'string' ? `/api/file/preview/${thumbnailId}` : null)}
              />
            </Form.Item>
            <Form.Item name="thumbnailId" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('COURSE_DESCRIPTION', 'Course Description')}
              rules={[{max: 5000, message: t('DESCRIPTION_TOO_LONG', 'Description cannot exceed 5000 characters')}]}
            >
              <TextArea rows={4} placeholder={t('ENTER_COURSE_DESCRIPTION', 'Enter course description')} />
            </Form.Item>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="simulationType"
                  label={t('SIMULATION_TYPE', 'Simulation Type')}
                  rules={[
                    {required: true, message: t('PLEASE_SELECT_SIMULATION_TYPE', 'Please select simulation type!')},
                  ]}
                >
                  <Select
                    placeholder={t('SELECT_SIMULATION_TYPE', 'Select simulation type')}
                    allowClear
                    onChange={handleSimulationTypeChange}
                  >
                    {SIMULATION_TYPES.map(type => (
                      <Option key={type} value={type}>
                        {t(type.toUpperCase() + '_SIMULATION', type)}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
          {isEditMode && (
            <Card
              title={t('REFERENCE_MATERIALS', 'Reference Materials')}
              extra={
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => setShowUploadReference(true)}
                >
                  {t('ADD_REFERENCE_MATERIALS', 'Add Reference Materials')}
                </AntButton>
              }
              style={{marginTop: 24, marginBottom: 24}}
            >
              <div className="reference-materials-section">
                {references.length === 0 ? (
                  <div className="reference-empty-state">
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={t('NO_REFERENCE_MATERIALS', 'Chưa có tài liệu tham khảo nào')}
                    >
                      <AntButton
                        type={BUTTON.DEEP_NAVY}
                        icon={<PlusOutlined />}
                        onClick={() => setShowUploadReference(true)}
                      >
                        {t('ADD_FIRST_REFERENCE', 'Thêm tài liệu đầu tiên')}
                      </AntButton>
                    </Empty>
                  </div>
                ) : (
                  <>
                    {/* Header với view controls */}
                    <div className="reference-controls">
                      <div className="reference-info">
                        <span className="reference-count">
                          {t('TOTAL_REFERENCES', 'Tổng cộng')}: <strong>{references.length}</strong> {t('DOCUMENTS', 'tài liệu')}
                        </span>
                      </div>
                      <div className="reference-view-controls">
                        <Button.Group size="small">
                          <Button
                            type={referenceViewMode === 'card' ? 'primary' : 'default'}
                            icon={<AppstoreOutlined />}
                            onClick={() => setReferenceViewMode('card')}
                          >
                            {t('CARD_VIEW', 'Thẻ')}
                          </Button>
                          <Button
                            type={referenceViewMode === 'list' ? 'primary' : 'default'}
                            icon={<UnorderedListOutlined />}
                            onClick={() => setReferenceViewMode('list')}
                          >
                            {t('LIST_VIEW', 'Danh sách')}
                          </Button>
                        </Button.Group>
                      </div>
                    </div>

                    {/* Render theo view mode */}
                    {referenceViewMode === 'card' ? (
                      // Card View với horizontal scroll
                      <div className="reference-cards-scroll-container">
                        <div className="reference-cards-scroll">
                          {references.map(reference => (
                            <Card
                              key={reference._id}
                              className="reference-card horizontal"
                              hoverable
                              size="small"
                            >
                              <div className="reference-card-content">
                                <div className="reference-header">
                                  <div className="reference-icon-wrapper">
                                    <div className="reference-icon">
                                      {getFileIcon(reference.name, reference.type)}
                                    </div>
                                  </div>
                                  <div className="reference-title">
                                    {reference.type === 'url' ? (
                                      <a
                                        href={reference.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="reference-link"
                                        title={reference.name || reference.url}
                                      >
                                        {reference.name || reference.url}
                                      </a>
                                    ) : (
                                      <a
                                        href={API.STREAM_ID.format(reference.fileId)}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="reference-link"
                                        title={reference.name}
                                      >
                                        {reference.name}
                                      </a>
                                    )}
                                  </div>
                                </div>

                                <div className="reference-meta">
                                  <div className="reference-tags">
                                    <Tag
                                      color={reference.type === 'url' ? 'blue' : 'green'}
                                      size="small"
                                    >
                                      {reference.type === 'url' ? t('URL_LINK', 'Link') : t('FILE', 'File')}
                                    </Tag>
                                    <Tag
                                      color={reference.isPublic ? 'success' : 'warning'}
                                      icon={reference.isPublic ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                                      size="small"
                                    >
                                      {reference.isPublic ? t('PUBLIC', 'Public') : t('PRIVATE', 'Private')}
                                    </Tag>
                                  </div>
                                  {reference.createdAt && (
                                    <div className="reference-date">
                                      <CalendarOutlined />
                                      <span>{formatDate(reference.createdAt)}</span>
                                    </div>
                                  )}
                                </div>

                                <div className="reference-actions">
                                  {reference.content && (
                                    <Tooltip title={t('VIEW_CONTENT', 'Xem nội dung')}>
                                      <Button
                                        type="text"
                                        icon={<FileTextOutlined />}
                                        onClick={() => {
                                          setSelectedReference(reference);
                                          setShowReferenceContent(true);
                                        }}
                                        className="reference-action-btn view-btn"
                                        size="small"
                                      />
                                    </Tooltip>
                                  )}
                                  {reference.type === 'file' && (
                                    <Tooltip title={t('DOWNLOAD', 'Tải xuống')}>
                                      <Button
                                        type="text"
                                        icon={<CloudDownloadOutlined />}
                                        onClick={() => window.open(API.STREAM_ID.format(reference.fileId), '_blank')}
                                        className="reference-action-btn download-btn"
                                        size="small"
                                      />
                                    </Tooltip>
                                  )}
                                  <Tooltip title={t('EDIT', 'Chỉnh sửa')}>
                                    <Button
                                      type="text"
                                      icon={<EditOutlined />}
                                      onClick={() => handleEditReference(reference)}
                                      className="reference-action-btn edit-btn"
                                      size="small"
                                    />
                                  </Tooltip>
                                  <Tooltip title={t('DELETE', 'Xóa')}>
                                    <Button
                                      type="text"
                                      icon={<DeleteOutlined />}
                                      onClick={() => handleRemoveReference(reference._id)}
                                      className="reference-action-btn delete-btn"
                                      size="small"
                                    />
                                  </Tooltip>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      </div>
                    ) : (
                      // List View - compact list
                      <div className="reference-list-view">
                        {references.map(reference => (
                          <div key={reference._id} className="reference-list-item">
                            <div className="reference-list-icon">
                              {getFileIcon(reference.name, reference.type)}
                            </div>
                            <div className="reference-list-content">
                              <div className="reference-list-title">
                                {reference.type === 'url' ? (
                                  <a
                                    href={reference.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="reference-list-link"
                                  >
                                    {reference.name || reference.url}
                                  </a>
                                ) : (
                                  <a
                                    href={API.STREAM_ID.format(reference.fileId)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="reference-list-link"
                                  >
                                    {reference.name}
                                  </a>
                                )}
                              </div>
                              <div className="reference-list-meta">
                                <Tag color={reference.type === 'url' ? 'blue' : 'green'} size="small">
                                  {reference.type === 'url' ? 'Link' : 'File'}
                                </Tag>
                                <Tag
                                  color={reference.isPublic ? 'success' : 'warning'}
                                  icon={reference.isPublic ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                                  size="small"
                                >
                                  {reference.isPublic ? 'Public' : 'Private'}
                                </Tag>
                                {reference.createdAt && (
                                  <span className="reference-list-date">
                                    {formatDate(reference.createdAt)}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="reference-list-actions">
                              {reference.content && (
                                <Tooltip title="Xem nội dung">
                                  <Button
                                    type="text"
                                    icon={<FileTextOutlined />}
                                    onClick={() => {
                                      setSelectedReference(reference);
                                      setShowReferenceContent(true);
                                    }}
                                    size="small"
                                  />
                                </Tooltip>
                              )}
                              {reference.type === 'file' && (
                                <Tooltip title="Tải xuống">
                                  <Button
                                    type="text"
                                    icon={<CloudDownloadOutlined />}
                                    onClick={() => window.open(API.STREAM_ID.format(reference.fileId), '_blank')}
                                    size="small"
                                  />
                                </Tooltip>
                              )}
                              <Tooltip title="Chỉnh sửa">
                                <Button
                                  type="text"
                                  icon={<EditOutlined />}
                                  onClick={() => handleEditReference(reference)}
                                  size="small"
                                />
                              </Tooltip>
                              <Tooltip title="Xóa">
                                <Button
                                  type="text"
                                  icon={<DeleteOutlined />}
                                  onClick={() => handleRemoveReference(reference._id)}
                                  size="small"
                                />
                              </Tooltip>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}
              </div>
            </Card>
          )}
          {/* AI Scenarios Tabs - quản lý AI Scenarios với tab interface */}
          {isEditMode && (
            <Card style={{marginTop: 24}} title={t('AI_SCENARIOS')}>
              <AIScenarioTabs
                ref={scenarioTabsRef}
                courseId={id}
                onScenariosChange={handleScenariosChange}
                selectedSimulationType={selectedSimulationType}
                onTaskAdd={handleAddTask}
                onTaskUpdate={handleTaskUpdate}
                onTaskDelete={handleTaskDelete}
              />
            </Card>
          )}

          {/* Cảnh báo về unsaved tasks đã được hiển thị trong AIScenarioTabs, không cần hiển thị lại ở đây */}

          <Row justify="end" style={{marginTop: 24}} gutter={16}>
            <Col>
              <AntButton
                size={'large'}
                type={BUTTON.TEXT}
                onClick={handleCancel}
                className="btn-cancel"
                disabled={saveButtonDisabled}
              >
                {t('CANCEL')}
              </AntButton>
            </Col>
            <Col>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size={'large'}
                htmlType="submit"
                className="btn-save"
                loading={isLoading && !isLoadingThumbnail}
                disabled={saveButtonDisabled}
              >
                {isEditMode ? t('SAVE_CHANGES') : t('CREATE_COURSE')}
              </AntButton>
            </Col>
          </Row>
        </AntForm>

        {/* Modal Upload Reference */}
        <UploadReference
          isShowUpload={isShowUploadReference}
          setShowUpload={setShowUploadReference}
          onReferenceAdded={handleReferenceAdded}
          courseId={id} // Truyền courseId từ params
        />

        {/* Loại bỏ Modal Create AI Persona vì đã chuyển sang AI Scenarios */}

        {/* Modal hiển thị nội dung reference */}
        <ReferenceContentModal
          isOpen={isShowReferenceContent}
          onClose={() => setShowReferenceContent(false)}
          reference={selectedReference}
        />

        {/* Modal edit reference */}
        <EditReferenceModal
          isOpen={isShowEditReference}
          onClose={() => setShowEditReference(false)}
          reference={editingReference}
          onReferenceUpdated={handleReferenceUpdated}
        />

        {/* Modal Google Sheets Import */}
        <GoogleSheetsImportModal
          isOpen={isShowGoogleSheetsImport}
          onClose={() => setShowGoogleSheetsImport(false)}
          onDataSelected={handleGoogleSheetsDataSelected}
        />
      </div>
    </Loading>
  );
};

export default CourseDetailScreen;
