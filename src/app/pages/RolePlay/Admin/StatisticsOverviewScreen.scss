.statistics-overview-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-header-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .statistics-header {
      .header-left {
        .header-info {
          .statistics-title {
            margin: 0 0 4px 0;
            font-weight: 600;
            font-size: 24px;
          }

          .statistics-description {
            color: #666;
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }

  .statistics-filter-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .filter-form-item {
        margin-bottom: 0;
      }

      .filter-info {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 8px;

        .filter-label {
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
        }
      }

      .filter-buttons-col {
        .filter-buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;

          .ant-btn {
            min-width: 100px;
            height: 40px;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid #d9d9d9 !important;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
              border-color: #40a9ff !important;
            }

            &:focus {
              border-color: #1890ff !important;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }

            // Specific styling for different button types
            &.ant-btn-default {
              background-color: #fff;
              color: #595959;

              &:hover {
                background-color: #f5f5f5;
                color: #262626;
              }
            }

            &.ant-btn-primary {
              background-color: #1890ff;
              border-color: #1890ff !important;
              color: #fff;

              &:hover {
                background-color: #40a9ff;
                border-color: #40a9ff !important;
              }
            }
          }
        }
      }

      // Custom DatePicker styling
      .ant-picker {
        height: 40px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-picker-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-picker-input > input {
          font-size: 14px;
        }
      }
    }
  }

  .platform-overview-card,
  .completion-rate-card,
  .popular-courses-card,
  .top-students-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head-title {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .completion-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .completion-details {
    padding: 20px;

    .detail-item {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .rank-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .course-name,
  .student-name {
    display: flex;
    align-items: center;
  }

  // Grid layout for cards
  .ant-row {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;

    .statistics-filter-card {
      .form-filter {
        .filter-buttons-col {
          margin-top: 16px;

          .filter-buttons {
            justify-content: stretch;

            .ant-btn {
              flex: 1;
            }
          }
        }

        .filter-info {
          justify-content: center;
          margin-top: 8px;
        }
      }
    }

    .completion-details {
      padding: 16px;
    }

    // Stack cards vertically on mobile
    .popular-courses-card,
    .top-students-card {
      margin-bottom: 16px;
    }
  }

  @media (max-width: 576px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .ant-statistic {
      text-align: center;
    }

    .statistics-filter-card {
      .form-filter {
        .filter-buttons {
          flex-direction: column;
          gap: 8px;
        }
      }
    }
  }

  // Table customization
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }

    // Responsive table
    @media (max-width: 768px) {
      .ant-table-content {
        overflow-x: auto;
      }
    }
  }

  // Statistics cards
  .ant-statistic {
    .ant-statistic-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: #666;
    }

    .ant-statistic-content {
      font-weight: 600;
    }
  }

  // Progress circle customization
  .ant-progress-circle {
    .ant-progress-text {
      font-weight: 600;
      font-size: 24px;
    }
  }

  // Tag customization
  .ant-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;

    &.ant-tag-blue {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-green {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-orange {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  // Card hover effects
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }

  // Loading state
  .ant-spin-nested-loading {
    .ant-spin-container {
      transition: opacity 0.3s ease;
    }
  }

  // Empty state
  .ant-empty {
    padding: 40px 20px;

    .ant-empty-description {
      color: #999;
    }
  }
}
