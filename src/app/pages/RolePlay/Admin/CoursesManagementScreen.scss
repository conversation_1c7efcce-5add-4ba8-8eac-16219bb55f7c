.courses-management-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .courses-management-info-card,
  .courses-management-search-card,
  .courses-management-table-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .courses-management-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .courses-management-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .courses-management-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey) --typo-colours-secondary-grey is not defined;
      margin-bottom: 0;
    }

    .btn-create-course {
      margin-left: auto;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      // Search buttons styling is now handled by global admin-filter-buttons class
    }
  }

  .courses-management-table-card {
    .ant-table {
      .ant-table-container {
        .ant-table-thead>tr>th {
          background: #fafafa;
        }
      }
    }

    .course-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .btn-view-statistics,
      .btn-edit-course,
      .btn-delete-course {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-size: 14px;
      }

      .btn-view-statistics {
        background-color: #f0f5ff;
        color: #1890ff;
        border-color: #d6e4ff;

        &:hover {
          background-color: #e6f7ff;
          color: #096dd9;
          border-color: #91d5ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }
      }

      .btn-edit-course {
        background-color: #f6ffed;
        color: #52c41a;
        border-color: #d9f7be;

        &:hover {
          background-color: #f0fff0;
          color: #389e0d;
          border-color: #b7eb8f;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
        }
      }

      .btn-delete-course {
        background-color: #fff2f0;
        color: #ff4d4f;
        border-color: #ffccc7;

        &:hover {
          background-color: #fff1f0;
          color: #cf1322;
          border-color: #ffa39e;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
        }
      }
    }

    .course-title-value {
      color: #1890ff;
      transition: color 0.3s;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}
