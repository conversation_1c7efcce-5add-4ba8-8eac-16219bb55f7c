.roleplay-instruction-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .roleplay-instruction-info-card,
  .roleplay-instruction-search-card,
  .roleplay-instruction-table-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .roleplay-instruction-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .roleplay-instruction-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .roleplay-instruction-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey) --typo-colours-secondary-grey is not defined;
      margin-bottom: 0;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      display: flex;
      justify-content: flex-end;

      // Search buttons styling is now handled by global admin-filter-buttons class
    }
  }

  .roleplay-instruction-actions {
    display: flex;
    justify-content: center;
    gap: 8px;

    .btn-edit,
    .btn-delete {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      border: 1px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-size: 14px;
    }

    .btn-edit {
      background-color: #f6ffed;
      color: #52c41a;
      border-color: #d9f7be;

      &:hover {
        background-color: #f0fff0;
        color: #389e0d;
        border-color: #b7eb8f;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
      }
    }

    .btn-delete {
      background-color: #fff2f0;
      color: #ff4d4f;
      border-color: #ffccc7;

      &:hover {
        background-color: #fff1f0;
        color: #cf1322;
        border-color: #ffa39e;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
      }
    }
  }

  .roleplay-instruction-table {
    .name-value {
      font-weight: 500;
    }

    .roleplay-instruction-table-row {
      &:hover {
        cursor: pointer;
      }
    }
  }
}
