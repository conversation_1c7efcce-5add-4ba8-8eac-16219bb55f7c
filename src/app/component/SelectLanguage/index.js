import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";

import { BUTTON, LANGUAGE } from "@constant";

import * as app from "@src/ducks/app.duck";

import VN_FLAG from "@src/asset/icon/flag/vn_square.svg";
import EN_FLAG from "@src/asset/icon/flag/en_square.svg";

import AntButton from "../AntButton";

import ChevronDown from "@src/app/component/SvgIcons/ChevronDown";

import './SelectLanguage.scss';
import { connect, useDispatch } from "react-redux";


const SelectLanguage = ({ user, ...props }) => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch ()

  const handleChangeLang = lang => {
    if (i18n.language !== lang) {
      props.setLanguage(lang);
      i18n.changeLanguage(lang);
      props.localizeTool();
    }
  };
  return <div className="select-language" id="js-language-header">
    <img
      className="language__flag"
      src={i18n.language === LANGUAGE.EN ? EN_FLAG : VN_FLAG}
      alt="" />
    <span className="language__text">{t("LANG_STUDENT")}</span>
    <Dropdown
      menu={{
        selectedKeys: [i18n.language],
        items: [
          { key: LANGUAGE.EN, label: t("ENG"), icon: <img src={EN_FLAG} alt="ENG" />, onClick: () => {
              handleChangeLang(LANGUAGE.EN);
              dispatch(actions.trackSelectLanguageResult(LANGUAGE.EN))
            } },
          { key: LANGUAGE.VI, label: t("VIE"), icon: <img src={VN_FLAG} alt="VN" />, onClick: () => {
              handleChangeLang(LANGUAGE.VI);
              dispatch(actions.trackSelectLanguageResult(LANGUAGE.VI))
            } },
        ],
      }}
      placement="bottomRight"
      className="language__dropdown-btn"
      trigger="click"
      getPopupContainer={() => document.getElementById("js-language-header")}
    >
      <AntButton type={BUTTON.GHOST_WHITE} size="xsmall" icon={<ChevronDown />} />
    </Dropdown>
  </div>
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...app.actions, 
};
export default connect(mapStateToProps, mapDispatchToProps)(SelectLanguage);
